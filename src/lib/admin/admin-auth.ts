import { adminAuth, adminDb } from "../firebase/server-init";
import { FirebaseCollections } from "../../constants/firebaseCollections";

/**
 * List of admin email addresses that have access to admin functionality
 * In production, this should be moved to environment variables or a secure database
 */
const ADMIN_EMAILS = [
	"<EMAIL>",
	"<EMAIL>",
	// Add more admin emails as needed
];

/**
 * Check if a user is an admin based on their email
 */
export async function isUserAdmin(uid: string): Promise<boolean> {
	try {
		// Get user record from Firebase Auth
		const userRecord = await adminAuth.getUser(uid);
		
		if (!userRecord.email) {
			return false;
		}

		// Check if email is in admin list
		return ADMIN_EMAILS.includes(userRecord.email);
	} catch (error) {
		console.error("Error checking admin status:", error);
		return false;
	}
}

/**
 * Verify admin access for server-side operations
 * Throws an error if user is not an admin
 */
export async function verifyAdminAccess(uid: string): Promise<void> {
	const isAdmin = await isUserAdmin(uid);
	
	if (!isAdmin) {
		throw new Error("Unauthorized: Admin access required");
	}
}

/**
 * Get admin user information
 */
export async function getAdminUserInfo(uid: string) {
	await verifyAdminAccess(uid);
	
	try {
		const userRecord = await adminAuth.getUser(uid);
		return {
			uid: userRecord.uid,
			email: userRecord.email,
			displayName: userRecord.displayName,
			isAdmin: true,
		};
	} catch (error) {
		console.error("Error getting admin user info:", error);
		throw new Error("Failed to get admin user information");
	}
}
