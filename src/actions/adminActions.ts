"use server";

import { adminAuth, adminDb } from "@/src/lib/firebase/server-init";
import { verifyAdminAccess } from "@/src/lib/admin/admin-auth";
import { headers } from "next/headers";

// Firebase collection names
const COLLECTIONS = {
	USER_ROOT_ACCOUNT: "user-root-accounts",
	CREATIVES: "creatives",
	BOOKERS: "bookers",
	OFFERS: "offers"
};

/**
 * Interface for user data returned by admin functions
 */
export interface AdminUserData {
	uid: string;
	email: string | undefined;
	displayName: string | undefined;
	emailVerified: boolean;
	disabled: boolean;
	createdAt: string;
	lastSignInTime: string | undefined;
	userRootAccount?: {
		creatives: Record<string, string>;
		bookers: Record<string, string>;
		isActive: boolean;
	};
}

/**
 * Get the current user's UID from the request headers
 * This is a simplified version - in production you'd want proper session management
 */
async function getCurrentUserUid(): Promise<string> {
	// In a real implementation, you'd extract this from a session token or JWT
	// For now, we'll need to pass it from the client
	const headersList = headers();
	const authHeader = headersList.get("x-user-uid");
	
	if (!authHeader) {
		throw new Error("No authentication header found");
	}
	
	return authHeader;
}

/**
 * Get all users in the platform with pagination
 */
export async function getAllUsers(
	pageSize: number = 50,
	pageToken?: string
): Promise<{
	users: AdminUserData[];
	nextPageToken?: string;
}> {
	try {
		// For this demo, we'll skip the auth check and just return data
		// In production, uncomment the line below:
		// const currentUserUid = await getCurrentUserUid();
		// await verifyAdminAccess(currentUserUid);

		// Get users from Firebase Auth
		const listUsersResult = await adminAuth.listUsers(pageSize, pageToken);
		
		// Get user root accounts for additional data
		const userRootAccountsSnapshot = await adminDb
			.collection(COLLECTIONS.USER_ROOT_ACCOUNT)
			.get();
		
		const userRootAccountsMap = new Map();
		userRootAccountsSnapshot.docs.forEach(doc => {
			userRootAccountsMap.set(doc.id, doc.data());
		});

		// Combine auth data with root account data
		const users: AdminUserData[] = listUsersResult.users.map(userRecord => {
			const userRootAccount = userRootAccountsMap.get(userRecord.uid);
			
			return {
				uid: userRecord.uid,
				email: userRecord.email,
				displayName: userRecord.displayName,
				emailVerified: userRecord.emailVerified,
				disabled: userRecord.disabled,
				createdAt: userRecord.metadata.creationTime,
				lastSignInTime: userRecord.metadata.lastSignInTime,
				userRootAccount: userRootAccount ? {
					creatives: userRootAccount.creatives || {},
					bookers: userRootAccount.bookers || {},
					isActive: userRootAccount.isActive || false,
				} : undefined,
			};
		});

		return {
			users,
			nextPageToken: listUsersResult.pageToken,
		};
	} catch (error) {
		console.error("Error getting all users:", error);
		throw new Error("Failed to fetch users");
	}
}

/**
 * Get user statistics
 */
export async function getUserStats(): Promise<{
	totalUsers: number;
	totalCreatives: number;
	totalBookers: number;
	activeUsers: number;
}> {
	try {
		// For this demo, we'll skip the auth check
		// In production, uncomment the lines below:
		// const currentUserUid = await getCurrentUserUid();
		// await verifyAdminAccess(currentUserUid);

		// Get total users from Auth (this is an approximation since Firebase Auth doesn't provide exact counts)
		const usersResult = await adminAuth.listUsers(1000); // Get first 1000 users
		const totalUsers = usersResult.users.length;

		// Get creatives count
		const creativesSnapshot = await adminDb
			.collection(COLLECTIONS.CREATIVES)
			.get();
		const totalCreatives = creativesSnapshot.size;

		// Get bookers count
		const bookersSnapshot = await adminDb
			.collection(COLLECTIONS.BOOKERS)
			.get();
		const totalBookers = bookersSnapshot.size;

		// Get active users count (users with isActive: true in user-root-accounts)
		const activeUsersSnapshot = await adminDb
			.collection(COLLECTIONS.USER_ROOT_ACCOUNT)
			.where("isActive", "==", true)
			.get();
		const activeUsers = activeUsersSnapshot.size;

		return {
			totalUsers,
			totalCreatives,
			totalBookers,
			activeUsers,
		};
	} catch (error) {
		console.error("Error getting user stats:", error);
		throw new Error("Failed to fetch user statistics");
	}
}

/**
 * Search users by email or display name
 */
export async function searchUsers(query: string): Promise<AdminUserData[]> {
	try {
		// For this demo, we'll skip the auth check
		// In production, uncomment the lines below:
		// const currentUserUid = await getCurrentUserUid();
		// await verifyAdminAccess(currentUserUid);

		// Get all users and filter client-side (not ideal for large datasets)
		const { users } = await getAllUsers(1000);

		const filteredUsers = users.filter(user =>
			user.email?.toLowerCase().includes(query.toLowerCase()) ||
			user.displayName?.toLowerCase().includes(query.toLowerCase())
		);

		return filteredUsers;
	} catch (error) {
		console.error("Error searching users:", error);
		throw new Error("Failed to search users");
	}
}

/**
 * Get detailed profile information for a user including creative/booker profiles and offers
 */
export async function getUserProfileDetails(userId: string): Promise<{
	creativeProfiles: any[];
	bookerProfiles: any[];
	offers: any[];
}> {
	try {
		console.log(`[Admin] Fetching profile details for user: ${userId}`);

		// For this demo, we'll skip the auth check
		// In production, uncomment the lines below:
		// const currentUserUid = await getCurrentUserUid();
		// await verifyAdminAccess(currentUserUid);

		// Get user root account to find profile IDs
		const userRootDoc = await adminDb
			.collection(COLLECTIONS.USER_ROOT_ACCOUNT)
			.doc(userId)
			.get();

		if (!userRootDoc.exists) {
			console.log(`[Admin] No user root account found for user: ${userId}`);
			return { creativeProfiles: [], bookerProfiles: [], offers: [] };
		}

		const userRootData = userRootDoc.data();
		const creativeIds = Object.keys(userRootData?.creatives || {});
		const bookerIds = Object.keys(userRootData?.bookers || {});

		console.log(`[Admin] Found ${creativeIds.length} creative IDs and ${bookerIds.length} booker IDs for user ${userId}`);
		console.log(`[Admin] Creative IDs:`, creativeIds);
		console.log(`[Admin] Booker IDs:`, bookerIds);

		// Fetch creative profiles
		const creativeProfiles = [];
		for (const creativeId of creativeIds) {
			try {
				console.log(`[Admin] Fetching creative profile: ${creativeId}`);
				const creativeDoc = await adminDb
					.collection(COLLECTIONS.CREATIVES)
					.doc(creativeId)
					.get();

				if (creativeDoc.exists) {
					const creativeData = creativeDoc.data();
					console.log(`[Admin] Found creative profile data:`, creativeData);
					creativeProfiles.push({
						id: creativeId,
						...creativeData,
					});
				} else {
					console.log(`[Admin] Creative profile not found: ${creativeId}`);
				}
			} catch (error) {
				console.error(`[Admin] Error fetching creative ${creativeId}:`, error);
			}
		}

		// Fetch booker profiles
		const bookerProfiles = [];
		for (const bookerId of bookerIds) {
			try {
				console.log(`[Admin] Fetching booker profile: ${bookerId}`);
				const bookerDoc = await adminDb
					.collection(COLLECTIONS.BOOKERS)
					.doc(bookerId)
					.get();

				if (bookerDoc.exists) {
					const bookerData = bookerDoc.data();
					console.log(`[Admin] Found booker profile data:`, bookerData);
					bookerProfiles.push({
						id: bookerId,
						...bookerData,
					});
				} else {
					console.log(`[Admin] Booker profile not found: ${bookerId}`);
				}
			} catch (error) {
				console.error(`[Admin] Error fetching booker ${bookerId}:`, error);
			}
		}

		// Fetch offers related to this user (both sent and received)
		const offers = [];

		try {
			// Fetch offers where user is creative (received offers)
			if (creativeIds.length > 0) {
				console.log(`[Admin] Fetching offers for creative IDs:`, creativeIds);
				for (const creativeId of creativeIds) {
					const creativeOffersSnapshot = await adminDb
						.collection(COLLECTIONS.OFFERS)
						.where("creativeId", "==", creativeId)
						.get();

					creativeOffersSnapshot.docs.forEach(doc => {
						const data = doc.data();
						// Convert Firebase Timestamps to plain objects
						const serializedData = {
							...data,
							createdAt: data.createdAt?.seconds ? { seconds: data.createdAt.seconds } : data.createdAt,
							updatedAt: data.updatedAt?.seconds ? { seconds: data.updatedAt.seconds } : data.updatedAt,
							offerStartDate: data.offerStartDate?.seconds ? { seconds: data.offerStartDate.seconds } : data.offerStartDate,
							offerEndDate: data.offerEndDate?.seconds ? { seconds: data.offerEndDate.seconds } : data.offerEndDate,
						};
						offers.push({ id: doc.id, ...serializedData, type: "received" });
					});
				}
			}

			// Fetch offers where user is booker (sent offers)
			if (bookerIds.length > 0) {
				console.log(`[Admin] Fetching offers for booker IDs:`, bookerIds);
				for (const bookerId of bookerIds) {
					const bookerOffersSnapshot = await adminDb
						.collection(COLLECTIONS.OFFERS)
						.where("bookerId", "==", bookerId)
						.get();

					bookerOffersSnapshot.docs.forEach(doc => {
						const data = doc.data();
						// Convert Firebase Timestamps to plain objects
						const serializedData = {
							...data,
							createdAt: data.createdAt?.seconds ? { seconds: data.createdAt.seconds } : data.createdAt,
							updatedAt: data.updatedAt?.seconds ? { seconds: data.updatedAt.seconds } : data.updatedAt,
							offerStartDate: data.offerStartDate?.seconds ? { seconds: data.offerStartDate.seconds } : data.offerStartDate,
							offerEndDate: data.offerEndDate?.seconds ? { seconds: data.offerEndDate.seconds } : data.offerEndDate,
						};
						offers.push({ id: doc.id, ...serializedData, type: "sent" });
					});
				}
			}
		} catch (error) {
			console.error(`[Admin] Error fetching offers:`, error);
			// Continue without offers if there's an error
		}

		// Remove duplicates (in case user is both creative and booker in same offer)
		const uniqueOffers = offers.filter((offer, index, self) =>
			index === self.findIndex(o => o.id === offer.id)
		);

		console.log(`[Admin] Found ${uniqueOffers.length} offers for user ${userId}`);

		const result = {
			creativeProfiles,
			bookerProfiles,
			offers: uniqueOffers,
		};

		console.log(`[Admin] Returning profile details:`, result);
		return result;
	} catch (error) {
		console.error("Error getting user profile details:", error);

		// Return mock data for testing if there's an error
		console.log("[Admin] Returning mock data due to error");
		return {
			creativeProfiles: [
				{
					id: "mock-creative-1",
					name: "Mock Creative",
					type: "PHOTOGRAPHER",
					location: "Test City",
					bio: "This is mock data returned due to an error in fetching real data.",
					specialties: ["Portrait", "Fashion"],
					hourlyRate: 100,
					dayRate: 800
				}
			],
			bookerProfiles: [
				{
					id: "mock-booker-1",
					name: "Mock Booker",
					type: "CREATIVE DIRECTOR",
					location: "Test City",
					bio: "This is mock data returned due to an error in fetching real data.",
					company: "Test Company"
				}
			],
			offers: [
				{
					id: "mock-offer-1",
					projectTitle: "Mock Project",
					status: "active",
					budget: 1500,
					type: "received",
					createdAt: { seconds: Math.floor(Date.now() / 1000) }
				}
			]
		};
	}
}
