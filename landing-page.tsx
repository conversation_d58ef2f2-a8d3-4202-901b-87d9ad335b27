"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON>alogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { auth } from "@/src/components/FirebaseComponents";
import { db } from "@/src/components/FirebaseComponents";
import { FirebaseCollections } from "@/src/constants/firebaseCollections";
import { Button as DuaButton } from "dua-component-library";
import {
	createUserWithEmailAndPassword,
	signInWithEmailAndPassword,
} from "firebase/auth";
import { doc, getDoc } from "firebase/firestore";
import { Briefcase, Search, Shield, User, Loader2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import type React from "react";
import { useState } from "react";

export default function LandingPage() {
	const [authModal, setAuthModal] = useState<"login" | "signup" | null>(null);
	const [userType, setUserType] = useState<"creative" | "booker">("creative");
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [firstName, setFirstName] = useState("");
	const [lastName, setLastName] = useState("");
	const [error, setError] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const router = useRouter();

	// Reset loading state when modal is closed
	const handleModalClose = (modalType: "login" | "signup", open: boolean) => {
		if (!open) {
			setIsLoading(false);
			setError(null);
		}
		setAuthModal(open ? modalType : null);
	};

	const handleLogin = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);
		setIsLoading(true);
		try {
			const userCredential = await signInWithEmailAndPassword(
				auth,
				email,
				password,
			);
			const user = userCredential.user;
			console.log("Login successful:", user);

			// Fetch the user's root account document to see which profiles they have access to.
			const userRootRef = doc(
				db,
				FirebaseCollections.USER_ROOT_ACCOUNT,
				user.uid,
			);
			const userRootSnap = await getDoc(userRootRef);

			if (!userRootSnap.exists()) {
				console.error("User logged in but no root account document found!");
				setError(
					"Your user profile is not configured correctly. Please contact support.",
				);
				return;
			}

			const userRootData = userRootSnap.data();
			const accessibleCreatives = userRootData.creatives
				? Object.keys(userRootData.creatives)
				: [];
			const accessibleBookers = userRootData.bookers
				? Object.keys(userRootData.bookers)
				: [];
			const totalProfiles =
				accessibleCreatives.length + accessibleBookers.length;

			if (totalProfiles === 0) {
				setError(
					"You do not have access to any profiles. Please contact support.",
				);
				return;
			}

			// If the user has access to exactly one profile, redirect them directly.
			if (totalProfiles === 1) {
				setAuthModal(null); // Close modal
				if (accessibleCreatives.length === 1) {
					router.push(`/creative/${accessibleCreatives[0]}`);
				} else {
					router.push(`/booker/${accessibleBookers[0]}`);
				}
				return;
			}

			// If the user has access to more than one profile, redirect to the selection page.
			setAuthModal(null); // Close modal
			router.push("/select-profile");
		} catch (error: any) {
			console.error("Login error:", error);
			const errorCode = error.code;
			if (
				errorCode === "auth/user-not-found" ||
				errorCode === "auth/wrong-password" ||
				errorCode === "auth/invalid-credential"
			) {
				setError("Invalid email or password. Please try again.");
			} else {
				setError("An unexpected error occurred during login.");
			}
		} finally {
			setIsLoading(false);
		}
	};

	const handleSignup = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);
		setIsLoading(true);
		try {
			const userCredential = await createUserWithEmailAndPassword(
				auth,
				email,
				password,
			);
			const user = userCredential.user;
			console.log("Signup successful:", user);

			if (userType === "creative") {
				// The creative is now created in a second step, just like the booker.
				// So, we just redirect to the create profile page.
				console.log(
					`User created in Auth. Redirecting to create creative profile page for user: ${user.uid}`,
				);

				// Clear form state and close modal before redirect
				setAuthModal(null);
				setEmail("");
				setPassword("");
				setFirstName("");
				setLastName("");

				router.push(`/creative/create-profile`);
			} else {
				// userType === 'booker'
				// For bookers, just create the auth user and redirect to the profile creation page.
				// The create_booker function will be called from that page.

				// Clear form state and close modal before redirect
				setAuthModal(null);
				setEmail("");
				setPassword("");
				setFirstName("");
				setLastName("");

				router.push(`/booker/create-profile`);
			}
		} catch (error: any) {
			console.error("Signup error:", error);
			setError(error.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="min-h-screen bg-white">
			{/* Header */}
			<header className="border-b border-gray-100">
				<div className="max-w-7xl mx-auto px-6 py-6 flex items-center justify-between">
					<nav className="flex items-center space-x-8">
						<Link
							href="#"
							className="text-gray-900 hover:text-gray-600 transition-colors"
						>
							Work
						</Link>
						<Link
							href="#"
							className="text-gray-900 hover:text-gray-600 transition-colors"
						>
							About
						</Link>
						<Link
							href="#"
							className="text-gray-900 hover:text-gray-600 transition-colors"
						>
							creatives
						</Link>
						<Link
							href="#"
							className="text-gray-900 hover:text-gray-600 transition-colors"
						>
							Bookers
						</Link>
						<Search className="h-5 w-5 text-gray-400 cursor-pointer hover:text-gray-600" />
					</nav>

					<div className="flex items-center space-x-6">
						<span className="text-2xl font-light tracking-wide">DUA</span>
						<div className="flex items-center space-x-4">
							<Dialog
								open={authModal === "login"}
								onOpenChange={(open) => handleModalClose("login", open)}
							>
								<DialogTrigger asChild>
									<Button variant="ghost" className="text-sm">
										Login
									</Button>
								</DialogTrigger>
								<DialogContent className="sm:max-w-md">
									<DialogHeader>
										<DialogTitle className="text-center">
											Welcome back
										</DialogTitle>
									</DialogHeader>
									<Tabs
										value={userType}
										onValueChange={(value) =>
											setUserType(value as "creative" | "booker")
										}
									>
										<TabsList className="grid w-full grid-cols-2">
											<TabsTrigger value="creative">Artist</TabsTrigger>
											<TabsTrigger value="booker">Booker</TabsTrigger>
										</TabsList>
										<TabsContent value="creative" className="space-y-4">
											<form onSubmit={handleLogin} className="space-y-4">
												<div className="space-y-2">
													<Label htmlFor="email">Email</Label>
													<Input
														id="email"
														type="email"
														value={email}
														onChange={(e) => setEmail(e.target.value)}
														disabled={isLoading}
														required
													/>
												</div>
												<div className="space-y-2">
													<Label htmlFor="password">Password</Label>
													<Input
														id="password"
														type="password"
														value={password}
														onChange={(e) => setPassword(e.target.value)}
														disabled={isLoading}
														required
													/>
												</div>
												{error && (
													<p className="text-red-500 text-sm">{error}</p>
												)}
												<Button type="submit" className="w-full" disabled={isLoading}>
													{isLoading ? (
														<>
															<Loader2 className="mr-2 h-4 w-4 animate-spin" />
															Signing In...
														</>
													) : (
														"Sign In"
													)}
												</Button>
											</form>
										</TabsContent>
										<TabsContent value="booker" className="space-y-4">
											<form onSubmit={handleLogin} className="space-y-4">
												<div className="space-y-2">
													<Label htmlFor="booker-email">Email</Label>
													<Input
														id="booker-email"
														type="email"
														value={email}
														onChange={(e) => setEmail(e.target.value)}
														disabled={isLoading}
														required
													/>
												</div>
												<div className="space-y-2">
													<Label htmlFor="booker-password">Password</Label>
													<Input
														id="booker-password"
														type="password"
														value={password}
														onChange={(e) => setPassword(e.target.value)}
														disabled={isLoading}
														required
													/>
												</div>
												{error && (
													<p className="text-red-500 text-sm">{error}</p>
												)}
												<Button type="submit" className="w-full" disabled={isLoading}>
													{isLoading ? (
														<>
															<Loader2 className="mr-2 h-4 w-4 animate-spin" />
															Signing In...
														</>
													) : (
														"Sign In"
													)}
												</Button>
											</form>
										</TabsContent>
									</Tabs>
								</DialogContent>
							</Dialog>

							<Dialog
								open={authModal === "signup"}
								onOpenChange={(open) => handleModalClose("signup", open)}
							>
								<DialogTrigger asChild>
									<Button className="text-sm">Join</Button>
								</DialogTrigger>
								<DialogContent className="sm:max-w-md">
									<DialogHeader>
										<DialogTitle className="text-center">Join DUA</DialogTitle>
									</DialogHeader>
									<Tabs
										value={userType}
										onValueChange={(value) =>
											setUserType(value as "creative" | "booker")
										}
									>
										<TabsList className="grid w-full grid-cols-2">
											<TabsTrigger value="creative">Artist</TabsTrigger>
											<TabsTrigger value="booker">Booker</TabsTrigger>
										</TabsList>
										<TabsContent value="creative" className="space-y-4">
											<form onSubmit={handleSignup} className="space-y-4">
												<div className="grid grid-cols-2 gap-4">
													<div className="space-y-2">
														<Label htmlFor="first-name">First Name</Label>
														<Input
															id="first-name"
															value={firstName}
															onChange={(e) => setFirstName(e.target.value)}
															disabled={isLoading}
															required
														/>
													</div>
													<div className="space-y-2">
														<Label htmlFor="last-name">Last Name</Label>
														<Input
															id="last-name"
															value={lastName}
															onChange={(e) => setLastName(e.target.value)}
															disabled={isLoading}
															required
														/>
													</div>
												</div>
												<div className="space-y-2">
													<Label htmlFor="creative-email">Email</Label>
													<Input
														id="creative-email"
														type="email"
														value={email}
														onChange={(e) => setEmail(e.target.value)}
														disabled={isLoading}
														required
													/>
												</div>
												<div className="space-y-2">
													<Label htmlFor="creative-password">Password</Label>
													<Input
														id="creative-password"
														type="password"
														value={password}
														onChange={(e) => setPassword(e.target.value)}
														disabled={isLoading}
														required
													/>
												</div>
												{error && (
													<p className="text-red-500 text-sm">{error}</p>
												)}
												<Button type="submit" className="w-full" disabled={isLoading}>
													{isLoading ? (
														<>
															<Loader2 className="mr-2 h-4 w-4 animate-spin" />
															Creating Account...
														</>
													) : (
														"Create Account"
													)}
												</Button>
											</form>
										</TabsContent>
										<TabsContent value="booker" className="space-y-4">
											<form onSubmit={handleSignup} className="space-y-4">
												<div className="grid grid-cols-2 gap-4">
													<div className="space-y-2">
														<Label htmlFor="booker-first-name">
															First Name
														</Label>
														<Input
															id="booker-first-name"
															value={firstName}
															onChange={(e) => setFirstName(e.target.value)}
															disabled={isLoading}
															required
														/>
													</div>
													<div className="space-y-2">
														<Label htmlFor="booker-last-name">Last Name</Label>
														<Input
															id="booker-last-name"
															value={lastName}
															onChange={(e) => setLastName(e.target.value)}
															disabled={isLoading}
															required
														/>
													</div>
												</div>
												<div className="space-y-2">
													<Label htmlFor="company">Company</Label>
													<Input id="company" disabled={isLoading} required />
												</div>
												<div className="space-y-2">
													<Label htmlFor="booker-signup-email">Email</Label>
													<Input
														id="booker-signup-email"
														type="email"
														value={email}
														onChange={(e) => setEmail(e.target.value)}
														disabled={isLoading}
														required
													/>
												</div>
												<div className="space-y-2">
													<Label htmlFor="booker-signup-password">
														Password
													</Label>
													<Input
														id="booker-signup-password"
														type="password"
														value={password}
														onChange={(e) => setPassword(e.target.value)}
														disabled={isLoading}
														required
													/>
												</div>
												{error && (
													<p className="text-red-500 text-sm">{error}</p>
												)}
												<Button type="submit" className="w-full" disabled={isLoading}>
													{isLoading ? (
														<>
															<Loader2 className="mr-2 h-4 w-4 animate-spin" />
															Creating Account...
														</>
													) : (
														"Create Account"
													)}
												</Button>
											</form>
										</TabsContent>
									</Tabs>
								</DialogContent>
							</Dialog>
						</div>
					</div>
				</div>
			</header>

			{/* Main Content Grid */}
			<main className="max-w-7xl mx-auto px-6 py-12">
				<div className="grid grid-cols-1 lg:grid-cols-3 gap-8 h-[80vh]">
					{/* Large Hero Section */}
					<div className="lg:col-span-2 relative group cursor-pointer overflow-hidden">
						<div className="absolute inset-0 bg-gradient-to-br from-slate-900 to-slate-700"></div>
						<div className="absolute inset-0 bg-black/20"></div>
						<div
							className="absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-105"
							style={{
								backgroundImage: `url('/placeholder.svg?height=600&width=800')`,
							}}
						></div>
						<div className="relative h-full flex flex-col justify-end p-12 text-white">
							<h1 className="text-5xl lg:text-6xl font-light mb-4 leading-tight">
								New Standard for Creatives,
								<br />
								Connect
								<br />
								Create
								<br />
								Collaborate
							</h1>
							<p className="text-xl font-light opacity-90">
								The platform for creative professionals
							</p>
							<div className="mt-6">
								<DuaButton>Get Started with DUA</DuaButton>
							</div>
						</div>
					</div>

					{/* Right Column */}
					<div className="space-y-6">
						{/* For creatives */}
						<div className="relative group cursor-pointer overflow-hidden h-64">
							<div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600"></div>
							<div
								className="absolute inset-0 bg-cover bg-center opacity-80 transition-transform duration-700 group-hover:scale-105"
								style={{
									backgroundImage: `url('/placeholder.svg?height=300&width=400')`,
								}}
							></div>
							<div className="relative h-full flex flex-col justify-end p-8 text-white">
								<div className="flex items-center mb-3">
									<User className="h-6 w-6 mr-3" />
									<span className="text-lg font-medium">For creatives</span>
								</div>
								<p className="text-sm opacity-90 leading-relaxed">
									Showcase your portfolio and connect with opportunities
								</p>
							</div>
						</div>

						{/* For Bookers */}
						<div className="relative group cursor-pointer overflow-hidden h-64">
							<div className="absolute inset-0 bg-gradient-to-br from-emerald-600 to-teal-600"></div>
							<div
								className="absolute inset-0 bg-cover bg-center opacity-80 transition-transform duration-700 group-hover:scale-105"
								style={{
									backgroundImage: `url('/placeholder.svg?height=300&width=400')`,
								}}
							></div>
							<div className="relative h-full flex flex-col justify-end p-8 text-white">
								<div className="flex items-center mb-3">
									<Briefcase className="h-6 w-6 mr-3" />
									<span className="text-lg font-medium">For Bookers</span>
								</div>
								<p className="text-sm opacity-90 leading-relaxed">
									Discover and hire exceptional creative talent
								</p>
							</div>
						</div>

						{/* Payment Guaranteed */}
						<div className="relative group cursor-pointer overflow-hidden h-64">
							<div className="absolute inset-0 bg-gradient-to-br from-amber-600 to-yellow-600"></div>
							<div
								className="absolute inset-0 bg-cover bg-center opacity-80 transition-transform duration-700 group-hover:scale-105"
								style={{
									backgroundImage: `url('/placeholder.svg?height=300&width=400')`,
								}}
							></div>
							<div className="relative h-full flex flex-col justify-end p-8 text-white">
								<div className="flex items-center mb-3">
									<Shield className="h-6 w-6 mr-3" />
									<span className="text-lg font-medium">
										Payment Guaranteed
									</span>
								</div>
								<p className="text-sm opacity-90 leading-relaxed">
									Secure payments with full protection for all transactions
								</p>
							</div>
						</div>
					</div>
				</div>

				{/* Bottom Section */}
				<div className="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
					<div className="relative group cursor-pointer overflow-hidden h-48">
						<div className="absolute inset-0 bg-gradient-to-br from-orange-500 to-red-500"></div>
						<div
							className="absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105"
							style={{
								backgroundImage: `url('/placeholder.svg?height=200&width=300')`,
							}}
						></div>
						<div className="relative h-full flex flex-col justify-end p-6 text-white">
							<h3 className="text-lg font-medium mb-2">Photography</h3>
							<p className="text-sm opacity-90">Professional photographers</p>
						</div>
					</div>

					<div className="relative group cursor-pointer overflow-hidden h-48">
						<div className="absolute inset-0 bg-gradient-to-br from-pink-500 to-rose-500"></div>
						<div
							className="absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105"
							style={{
								backgroundImage: `url('/placeholder.svg?height=200&width=300')`,
							}}
						></div>
						<div className="relative h-full flex flex-col justify-end p-6 text-white">
							<h3 className="text-lg font-medium mb-2">Design</h3>
							<p className="text-sm opacity-90">Creative visual designers</p>
						</div>
					</div>

					<div className="relative group cursor-pointer overflow-hidden h-48">
						<div className="absolute inset-0 bg-gradient-to-br from-indigo-500 to-blue-500"></div>
						<div
							className="absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105"
							style={{
								backgroundImage: `url('/placeholder.svg?height=200&width=300')`,
							}}
						></div>
						<div className="relative h-full flex flex-col justify-end p-6 text-white">
							<h3 className="text-lg font-medium mb-2">Video</h3>
							<p className="text-sm opacity-90">Video production experts</p>
						</div>
					</div>

					<div className="relative group cursor-pointer overflow-hidden h-48">
						<div className="absolute inset-0 bg-gradient-to-br from-violet-500 to-purple-500"></div>
						<div
							className="absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105"
							style={{
								backgroundImage: `url('/placeholder.svg?height=200&width=300')`,
							}}
						></div>
						<div className="relative h-full flex flex-col justify-end p-6 text-white">
							<h3 className="text-lg font-medium mb-2">Content</h3>
							<p className="text-sm opacity-90">Content creators & writers</p>
						</div>
					</div>
				</div>
			</main>

			{/* Footer */}
			<footer className="border-t border-gray-100 mt-20">
				<div className="max-w-7xl mx-auto px-6 py-12">
					<div className="grid grid-cols-1 md:grid-cols-4 gap-8">
						<div>
							<h3 className="font-medium mb-4">Platform</h3>
							<ul className="space-y-2 text-sm text-gray-600">
								<li>
									<Link href="#" className="hover:text-gray-900">
										How it works
									</Link>
								</li>
								<li>
									<Link href="#" className="hover:text-gray-900">
										Pricing
									</Link>
								</li>
								<li>
									<Link href="#" className="hover:text-gray-900">
										Success stories
									</Link>
								</li>
							</ul>
						</div>
						<div>
							<h3 className="font-medium mb-4">For creatives</h3>
							<ul className="space-y-2 text-sm text-gray-600">
								<li>
									<Link href="#" className="hover:text-gray-900">
										Create portfolio
									</Link>
								</li>
								<li>
									<Link href="#" className="hover:text-gray-900">
										Find work
									</Link>
								</li>
								<li>
									<Link href="#" className="hover:text-gray-900">
										Resources
									</Link>
								</li>
							</ul>
						</div>
						<div>
							<h3 className="font-medium mb-4">For Bookers</h3>
							<ul className="space-y-2 text-sm text-gray-600">
								<li>
									<Link href="#" className="hover:text-gray-900">
										Find talent
									</Link>
								</li>
								<li>
									<Link href="#" className="hover:text-gray-900">
										Post projects
									</Link>
								</li>
								<li>
									<Link href="#" className="hover:text-gray-900">
										Hiring guide
									</Link>
								</li>
							</ul>
						</div>
						<div>
							<h3 className="font-medium mb-4">Support</h3>
							<ul className="space-y-2 text-sm text-gray-600">
								<li>
									<Link href="#" className="hover:text-gray-900">
										Help center
									</Link>
								</li>
								<li>
									<Link href="#" className="hover:text-gray-900">
										Contact
									</Link>
								</li>
								<li>
									<Link href="/terms" className="hover:text-gray-900">
										Terms & Conditions
									</Link>
								</li>
								<li>
									<Link href="/privacy" className="hover:text-gray-900">
										Privacy
									</Link>
								</li>
							</ul>
						</div>
					</div>
					<div className="border-t border-gray-100 mt-8 pt-8 text-center text-sm text-gray-500">
						<p>&copy; 2024 DUA. All rights reserved.</p>
						<div className="mt-4 space-x-4">
							<Link href="/terms" className="hover:text-gray-700">
								Terms & Conditions
							</Link>
							<span>•</span>
							<Link href="/privacy" className="hover:text-gray-700">
								Privacy Policy
							</Link>
						</div>
					</div>
				</div>
			</footer>
		</div>
	);
}
