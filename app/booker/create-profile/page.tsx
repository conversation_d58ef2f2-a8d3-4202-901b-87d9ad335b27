"use client";

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
	Card,
	Card<PERSON>ontent,
	CardFooter,
	Card<PERSON>eader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { callFirebaseFunction } from "@/src/lib/firebaseFunctions";
import type { CreateBookerRequest } from "@/types/requests/bookerRequestInterfaces";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useUser } from "reactfire";

export default function CreateBookerProfilePage() {
	const { status, data: user } = useUser();
	const router = useRouter();
	const [error, setError] = useState<string | null>(null);
	const [formData, setFormData] = useState<Partial<CreateBookerRequest>>({
		displayName: "",
		phone: "",
		address: {
			streetName: "",
			zipCode: "",
			city: "",
			country: "",
		},
		payoutAccount: {
			accountHolderName: "",
			bankName: "",
			iban: "",
			taxResidenceCountry: "",
		},
	});

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		const nameParts = name.split(".");

		if (nameParts.length > 1) {
			const [parent, child] = nameParts;
			setFormData((prev: Partial<CreateBookerRequest>) => ({
				...prev,
				[parent]: {
					...(prev[parent as keyof typeof prev] as object),
					[child]: value,
				},
			}));
		} else {
			setFormData((prev: Partial<CreateBookerRequest>) => ({
				...prev,
				[name]: value,
			}));
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);

		if (!user) {
			setError("You must be logged in to create a profile.");
			return;
		}

		const payload: CreateBookerRequest = {
			...formData,
			email: user.email!,
		} as CreateBookerRequest;

		try {
			console.log("Creating booker profile with payload:", payload);
			await callFirebaseFunction("create_booker", payload);
			console.log("Booker profile created successfully!");
			router.push(`/booker/${user.uid}`);
		} catch (err: any) {
			console.error("Error creating booker profile:", err);
			setError(err.message || "An unexpected error occurred.");
		}
	};

	if (status === "loading") {
		return <div>Loading user...</div>;
	}

	if (!user) {
		router.push("/landing-page"); // Or your login page
		return <div>Redirecting to login...</div>;
	}

	return (
		<div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
			<Card className="w-full max-w-4xl">
				<CardHeader>
					<CardTitle className="text-2xl font-bold text-center">
						Complete Your Booker Profile
					</CardTitle>
				</CardHeader>
				<form onSubmit={handleSubmit}>
					<CardContent className="space-y-8">
						{/* Contact Information */}
						<div className="space-y-4">
							<h3 className="text-lg font-semibold">Contact Information</h3>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div className="space-y-2">
									<Label htmlFor="displayName">Display Name</Label>
									<Input
										id="displayName"
										name="displayName"
										value={formData.displayName}
										onChange={handleChange}
										required
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="phone">Phone Number</Label>
									<Input
										id="phone"
										name="phone"
										value={formData.phone}
										onChange={handleChange}
										required
									/>
								</div>
							</div>
						</div>

						{/* Address */}
						<div className="space-y-4">
							<h3 className="text-lg font-semibold">Address</h3>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<Input
									placeholder="Street Name"
									name="address.streetName"
									value={formData.address?.streetName}
									onChange={handleChange}
									required
								/>
								<Input
									placeholder="City"
									name="address.city"
									value={formData.address?.city}
									onChange={handleChange}
									required
								/>
								<Input
									placeholder="ZIP Code"
									name="address.zipCode"
									value={formData.address?.zipCode}
									onChange={handleChange}
									required
								/>
								<Input
									placeholder="Country"
									name="address.country"
									value={formData.address?.country}
									onChange={handleChange}
									required
								/>
							</div>
						</div>

						{/* Payout Information */}
						<div className="space-y-4">
							<h3 className="text-lg font-semibold">Payout Account</h3>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<Input
									placeholder="Account Holder Name"
									name="payoutAccount.accountHolderName"
									value={formData.payoutAccount?.accountHolderName}
									onChange={handleChange}
									required
								/>
								<Input
									placeholder="Bank Name"
									name="payoutAccount.bankName"
									value={formData.payoutAccount?.bankName}
									onChange={handleChange}
									required
								/>
								<Input
									placeholder="IBAN"
									name="payoutAccount.iban"
									value={formData.payoutAccount?.iban}
									onChange={handleChange}
									required
								/>
								<Input
									placeholder="Country of Tax Residence"
									name="payoutAccount.taxResidenceCountry"
									value={formData.payoutAccount?.taxResidenceCountry}
									onChange={handleChange}
									required
								/>
							</div>
						</div>
					</CardContent>
					<CardFooter className="flex flex-col gap-4">
						{error && (
							<Alert variant="destructive">
								<AlertDescription>{error}</AlertDescription>
							</Alert>
						)}
						<Button type="submit" className="w-full">
							Create Profile
						</Button>
					</CardFooter>
				</form>
			</Card>
		</div>
	);
}
