"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON>ooter,
	Card<PERSON>eader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { callFirebaseFunction } from "@/src/lib/firebaseFunctions";
import type { CreateBookerRequest } from "@/types/requests/bookerRequestInterfaces";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useUser } from "reactfire";
import Link from "next/link";
import { Textarea } from "@/components/ui/textarea";

interface PendingBookerProfile {
	firstName: string;
	lastName: string;
	email: string;
	company?: string;
	uid: string;
}

export default function CreateBookerProfilePage() {
	const { status, data: user } = useUser();
	const router = useRouter();
	const [error, setError] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [pendingProfile, setPendingProfile] = useState<PendingBookerProfile | null>(null);
	const [formData, setFormData] = useState<Partial<CreateBookerRequest>>({
		display_name: "",
		phone_number: "",
		company: "",
		bio: "",
		address: {
			streetName: "",
			zipCode: "",
			city: "",
			country: "",
		},
		payoutAccount: {
			accountHolderName: "",
			bankName: "",
			iban: "",
			taxResidenceCountry: "",
		},
	});

	// Load pending profile data from localStorage
	useEffect(() => {
		const pendingData = localStorage.getItem('pendingBookerProfile');
		if (pendingData) {
			const parsed = JSON.parse(pendingData) as PendingBookerProfile;
			setPendingProfile(parsed);

			// Pre-fill form with signup data
			setFormData(prev => ({
				...prev,
				display_name: `${parsed.firstName} ${parsed.lastName}`,
				company: parsed.company || "",
			}));
		}
	}, []);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { name, value } = e.target;
		const nameParts = name.split(".");

		if (nameParts.length > 1) {
			const [parent, child] = nameParts;
			setFormData((prev: Partial<CreateBookerRequest>) => ({
				...prev,
				[parent]: {
					...(prev[parent as keyof typeof prev] as object),
					[child]: value,
				},
			}));
		} else {
			setFormData((prev: Partial<CreateBookerRequest>) => ({
				...prev,
				[name]: value,
			}));
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);
		setIsLoading(true);

		if (!user) {
			setError("You must be logged in to create a profile.");
			setIsLoading(false);
			return;
		}

		if (!pendingProfile) {
			setError("Missing signup information. Please start the signup process again.");
			setIsLoading(false);
			return;
		}

		// Validate required fields
		if (!formData.display_name?.trim()) {
			setError("Display name is required.");
			setIsLoading(false);
			return;
		}

		// Prepare payload according to the Firebase endpoints documentation
		const payload = {
			display_name: formData.display_name,
			phone_number: formData.phone_number || "",
			company: formData.company || "",
			bio: formData.bio || "",
		};

		try {
			console.log("Creating booker profile with payload:", payload);
			await callFirebaseFunction("create_booker", payload);
			console.log("Booker profile created successfully!");

			// Clear the pending profile data
			localStorage.removeItem('pendingBookerProfile');

			// Redirect to booker profile page
			router.push(`/booker/${user.uid}`);
		} catch (err: any) {
			console.error("Error creating booker profile:", err);
			setError(err.message || "An unexpected error occurred.");
		} finally {
			setIsLoading(false);
		}
	};

	if (status === "loading") {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
					<p>Loading...</p>
				</div>
			</div>
		);
	}

	if (!user) {
		router.push("/");
		return <div>Redirecting to login...</div>;
	}

	return (
		<div className="min-h-screen bg-gray-50 py-12 px-4">
			<div className="max-w-4xl mx-auto">
				<Card className="shadow-lg">
					<CardHeader className="text-center">
						<CardTitle className="text-3xl font-bold text-gray-900">
							Complete Your Booker Profile
						</CardTitle>
						<p className="text-gray-600 mt-2">
							Tell us about yourself and your business needs
						</p>
						{pendingProfile && (
							<p className="text-sm text-green-600 mt-2">
								Welcome, {pendingProfile.firstName}! Let's set up your profile.
							</p>
						)}
					</CardHeader>

					<form onSubmit={handleSubmit}>
						<CardContent className="space-y-8">
							{/* Basic Information */}
							<div className="space-y-6">
								<h3 className="text-xl font-semibold text-gray-900 border-b pb-2">
									Basic Information
								</h3>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									<div className="space-y-2">
										<Label htmlFor="display_name">Display Name *</Label>
										<Input
											id="display_name"
											name="display_name"
											value={formData.display_name}
											onChange={handleChange}
											required
											placeholder="Your professional name"
										/>
									</div>
									<div className="space-y-2">
										<Label htmlFor="phone_number">Phone Number</Label>
										<Input
											id="phone_number"
											name="phone_number"
											value={formData.phone_number}
											onChange={handleChange}
											placeholder="+1234567890"
										/>
									</div>
								</div>

								<div className="space-y-2">
									<Label htmlFor="company">Company/Organization</Label>
									<Input
										id="company"
										name="company"
										value={formData.company}
										onChange={handleChange}
										placeholder="Your company or organization name"
									/>
								</div>

								<div className="space-y-2">
									<Label htmlFor="bio">About You</Label>
									<Textarea
										id="bio"
										name="bio"
										value={formData.bio}
										onChange={handleChange}
										placeholder="Tell us about your business, the types of projects you work on, and what you're looking for in creative talent..."
										rows={4}
									/>
								</div>
							</div>

						{/* Next Steps Info */}
						<div className="bg-green-50 border border-green-200 rounded-lg p-6">
							<h3 className="text-lg font-semibold text-green-900 mb-2">
								What happens next?
							</h3>
							<ul className="text-sm text-green-800 space-y-1">
								<li>• Your profile will be created and ready to use immediately</li>
								<li>• You can start searching for creative talent right away</li>
								<li>• Create projects and send offers to creatives</li>
								<li>• You can always update your profile information later</li>
							</ul>
						</div>

					</CardContent>
					<CardFooter className="flex flex-col gap-4">
						{error && (
							<Alert variant="destructive">
								<AlertDescription>{error}</AlertDescription>
							</Alert>
						)}

						<Button
							type="submit"
							className="w-full bg-green-600 hover:bg-green-700"
							disabled={isLoading}
							size="lg"
						>
							{isLoading ? (
								<>
									<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
									Creating Profile...
								</>
							) : (
								"Create Booker Profile"
							)}
						</Button>

						<p className="text-xs text-gray-500 text-center">
							By creating your profile, you agree to our{" "}
							<Link href="/terms" className="text-green-600 hover:text-green-700">
								Terms & Conditions
							</Link>
						</p>
					</CardFooter>
				</form>
			</Card>
		</div>
	);
}
