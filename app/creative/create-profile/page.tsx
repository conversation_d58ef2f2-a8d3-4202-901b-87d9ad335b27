"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Footer,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { callFirebaseFunction } from "@/src/lib/firebaseFunctions";
import type { CreateCreativeRequest } from "@/types/requests/creativeRequestInterfaces";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useUser } from "reactfire";

const creativeTypes = ["Photographer", "Videographer", "Model", "Stylist", "Makeup Artist", "Hair Stylist", "Director", "Producer"];

export default function CreateCreativeProfilePage() {
  const { status, data: user } = useUser();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<CreateCreativeRequest>>({
    displayName: "",
    description: "",
    creativeType: "",
    tags: [],
    availabilityNotes: "",
    address: {
      streetName: "",
      zipCode: "",
      city: "",
      country: "",
    },
    payoutAccount: {
      accountHolderName: "",
      bankName: "",
      iban: "",
      taxResidenceCountry: "",
    },
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const nameParts = name.split(".");

    if (nameParts.length > 1) {
      const [parent, child] = nameParts;
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof typeof prev] as object),
          [child]: value,
        },
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };
  
  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tags = e.target.value.split(',').map(tag => tag.trim());
    setFormData(prev => ({...prev, tags}));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!user) {
      setError("You must be logged in to create a profile.");
      return;
    }

    const payload: CreateCreativeRequest = {
      ...formData,
      email: user.email!,
    } as CreateCreativeRequest;

    try {
      console.log("Creating creative profile with payload:", payload);
      await callFirebaseFunction("create_creative", payload);
      console.log("Creative profile created successfully!");
      router.push(`/creative/${user.uid}`);
    } catch (err: any) {
      console.error("Error creating creative profile:", err);
      setError(err.message || "An unexpected error occurred.");
    }
  };

  if (status === "loading") {
    return <div>Loading user...</div>;
  }

  if (!user) {
    router.push("/landing-page");
    return <div>Redirecting to login...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Complete Your Creative Profile
          </CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-8">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="displayName">Display Name</Label>
                  <Input id="displayName" name="displayName" value={formData.displayName} onChange={handleChange} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="creativeType">Creative Type</Label>
                  <Select name="creativeType" onValueChange={(value) => setFormData(prev => ({...prev, creativeType: value}))} required>
                    <SelectTrigger><SelectValue placeholder="Select your specialty" /></SelectTrigger>
                    <SelectContent>
                      {creativeTypes.map(type => <SelectItem key={type} value={type}>{type}</SelectItem>)}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea id="description" name="description" value={formData.description} onChange={handleChange} placeholder="A brief bio..." />
              </div>
              <div className="space-y-2">
                  <Label htmlFor="tags">Tags (comma-separated)</Label>
                  <Input id="tags" name="tags" onChange={handleTagsChange} placeholder="e.g., fashion, beauty, lifestyle" />
              </div>
            </div>

            {/* Address */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Address</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input placeholder="Street Name" name="address.streetName" value={formData.address?.streetName} onChange={handleChange} required />
                <Input placeholder="City" name="address.city" value={formData.address?.city} onChange={handleChange} required />
                <Input placeholder="ZIP Code" name="address.zipCode" value={formData.address?.zipCode} onChange={handleChange} required />
                <Input placeholder="Country" name="address.country" value={formData.address?.country} onChange={handleChange} required />
              </div>
            </div>

            {/* Payout Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Payout Account</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input placeholder="Account Holder Name" name="payoutAccount.accountHolderName" value={formData.payoutAccount?.accountHolderName} onChange={handleChange} required />
                <Input placeholder="Bank Name" name="payoutAccount.bankName" value={formData.payoutAccount?.bankName} onChange={handleChange} required />
                <Input placeholder="IBAN" name="payoutAccount.iban" value={formData.payoutAccount?.iban} onChange={handleChange} required />
                <Input placeholder="Country of Tax Residence" name="payoutAccount.taxResidenceCountry" value={formData.payoutAccount?.taxResidenceCountry} onChange={handleChange} required />
              </div>
            </div>

            {/* Availability */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Availability</h3>
              <div className="space-y-2">
                  <Label htmlFor="availabilityNotes">Availability Notes</Label>
                  <Textarea id="availabilityNotes" name="availabilityNotes" value={formData.availabilityNotes} onChange={handleChange} placeholder="e.g., Available Mon-Wed, Booked until July..." />
              </div>
            </div>

          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            <Button type="submit" className="w-full">
              Create Profile
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
