"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { callFirebaseFunction } from "@/src/lib/firebaseFunctions";
import type { CreateCreativeRequest } from "@/types/requests/creativeRequestInterfaces";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useUser } from "reactfire";
import { useEffect } from "react";
import Link from "next/link";

const creativeTypes = ["PHOTOGRAPHER", "VIDEOGRAPHER", "MOD<PERSON>", "MAKEUP_ARTIST", "HAIR_STYLIST", "STYLIST", "DIRECTOR", "PRODUCER"];

interface PendingCreativeProfile {
  firstName: string;
  lastName: string;
  email: string;
  uid: string;
}

export default function CreateCreativeProfilePage() {
  const { status, data: user } = useUser();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [pendingProfile, setPendingProfile] = useState<PendingCreativeProfile | null>(null);
  const [formData, setFormData] = useState<Partial<CreateCreativeRequest>>({
    display_name: "",
    story: "",
    creative_type: [],
    instagram: "",
    phone_number: "",
    address: {
      streetName: "",
      zipCode: "",
      city: "",
      country: "",
    },
    payoutAccount: {
      accountHolderName: "",
      bankName: "",
      iban: "",
      taxResidenceCountry: "",
    },
  });

  // Load pending profile data from localStorage
  useEffect(() => {
    const pendingData = localStorage.getItem('pendingCreativeProfile');
    if (pendingData) {
      const parsed = JSON.parse(pendingData) as PendingCreativeProfile;
      setPendingProfile(parsed);

      // Pre-fill display name with first and last name
      setFormData(prev => ({
        ...prev,
        display_name: `${parsed.firstName} ${parsed.lastName}`,
      }));
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const nameParts = name.split(".");

    if (nameParts.length > 1) {
      const [parent, child] = nameParts;
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof typeof prev] as object),
          [child]: value,
        },
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleCreativeTypeChange = (value: string) => {
    const currentTypes = formData.creative_type || [];
    const updatedTypes = currentTypes.includes(value)
      ? currentTypes.filter(type => type !== value)
      : [...currentTypes, value];

    setFormData(prev => ({ ...prev, creative_type: updatedTypes }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    if (!user) {
      setError("You must be logged in to create a profile.");
      setIsLoading(false);
      return;
    }

    if (!pendingProfile) {
      setError("Missing signup information. Please start the signup process again.");
      setIsLoading(false);
      return;
    }

    // Validate required fields
    if (!formData.display_name?.trim()) {
      setError("Display name is required.");
      setIsLoading(false);
      return;
    }

    if (!formData.creative_type || formData.creative_type.length === 0) {
      setError("Please select at least one creative type.");
      setIsLoading(false);
      return;
    }

    // Prepare payload according to the Firebase endpoints documentation
    const payload = {
      display_name: formData.display_name,
      story: formData.story || "",
      instagram: formData.instagram || "",
      phone_number: formData.phone_number || "",
      creative_type: formData.creative_type,
    };

    try {
      console.log("Creating creative profile with payload:", payload);

      // Step 1: Call create_creative endpoint
      await callFirebaseFunction("create_creative", payload);
      console.log("Creative profile created successfully!");

      // Clear the pending profile data
      localStorage.removeItem('pendingCreativeProfile');

      // Step 2: Redirect to profile page (admin approval will be handled in the background)
      router.push(`/creative/${user.uid}`);

    } catch (err: any) {
      console.error("Error creating creative profile:", err);
      setError(err.message || "An unexpected error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push("/");
    return <div>Redirecting to login...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold text-gray-900">
              Complete Your Creative Profile
            </CardTitle>
            <p className="text-gray-600 mt-2">
              Tell us about yourself and your creative specialties
            </p>
            {pendingProfile && (
              <p className="text-sm text-blue-600 mt-2">
                Welcome, {pendingProfile.firstName}! Let's set up your profile.
              </p>
            )}
          </CardHeader>

          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-8">
              {/* Basic Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 border-b pb-2">
                  Basic Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="display_name">Display Name *</Label>
                    <Input
                      id="display_name"
                      name="display_name"
                      value={formData.display_name}
                      onChange={handleChange}
                      required
                      placeholder="Your professional name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone_number">Phone Number</Label>
                    <Input
                      id="phone_number"
                      name="phone_number"
                      value={formData.phone_number}
                      onChange={handleChange}
                      placeholder="+1234567890"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="story">Your Story</Label>
                  <Textarea
                    id="story"
                    name="story"
                    value={formData.story}
                    onChange={handleChange}
                    placeholder="Tell us about your creative journey, experience, and what makes you unique..."
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="instagram">Instagram Handle</Label>
                  <Input
                    id="instagram"
                    name="instagram"
                    value={formData.instagram}
                    onChange={handleChange}
                    placeholder="your_instagram_handle"
                  />
                </div>
              </div>

              {/* Creative Types */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 border-b pb-2">
                  Creative Specialties *
                </h3>
                <p className="text-sm text-gray-600">
                  Select all that apply to your creative work
                </p>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {creativeTypes.map(type => (
                    <div key={type} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={type}
                        checked={formData.creative_type?.includes(type) || false}
                        onChange={() => handleCreativeTypeChange(type)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Label htmlFor={type} className="text-sm font-medium">
                        {type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Next Steps Info */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">
                  What happens next?
                </h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Your profile will be submitted for admin review</li>
                  <li>• You'll receive an email notification once approved</li>
                  <li>• After approval, you can set your pricing and start receiving bookings</li>
                  <li>• You can always update your profile information later</li>
                </ul>
              </div>

            </CardContent>
            <CardFooter className="flex flex-col gap-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
                size="lg"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating Profile...
                  </>
                ) : (
                  "Create Creative Profile"
                )}
              </Button>

              <p className="text-xs text-gray-500 text-center">
                By creating your profile, you agree to our{" "}
                <Link href="/terms" className="text-blue-600 hover:text-blue-700">
                  Terms & Conditions
                </Link>
              </p>
            </CardFooter>
        </form>
      </Card>
    </div>
  );
}
